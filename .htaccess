# CamboPay URL Rewriting Rules
# Enable URL rewriting
RewriteEngine On

# Security: Block access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Block access to sensitive directories
RewriteRule ^(includes|admin/includes|vendor)/ - [F,L]

# Block access to configuration files
<FilesMatch "\.(env|ini|log|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# ============================================
# SMM Payment Request Rewriting
# ============================================

# Handle Request directory routing first
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^Request/(.*)$ Request/$1 [QSA,L]

# ============================================
# Specific Clean URL Routing (Process First)
# ============================================

# reCAPTCHA setup and test routing (must come before general .php removal)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^setup-recaptcha/?$ setup_recaptcha.php [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^test-recaptcha/?$ test_recaptcha.php [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^check-database/?$ check_database.php [QSA,L]

# ============================================
# Hide .php Extensions
# ============================================

# Remove .php extension from URLs (but skip Request directory)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !^/Request/
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect .php URLs to clean URLs (preserve query parameters)
# Skip webhook files and setup scripts to avoid breaking POST requests
RewriteCond %{THE_REQUEST} /([^.]+)\.php(\?[^\s]*)?\s [NC]
RewriteCond %{REQUEST_URI} !^/telegram_webhook\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/setup_telegram_webhook\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/test_telegram_webhook\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/setup_recaptcha\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/test_recaptcha\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/check_database\.php$ [NC]
RewriteRule ^ /%1%2? [NC,L,R=301]

# ============================================
# Clean URL Routing for Main Pages
# ============================================

# Redirect /index to root
RewriteCond %{THE_REQUEST} \s/+index[\s?] [NC]
RewriteRule ^index/?$ /? [R=301,L]

# Dashboard routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^dashboard/?$ user/dashboard.php [QSA,L]

# Admin dashboard routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/?$ admin/dashboard.php [QSA,L]

# API documentation routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api-docs/?$ api-docs.php [QSA,L]

# User profile routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^profile/?$ user/profile.php [QSA,L]

# Transactions routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^transactions/?$ user/transactions.php [QSA,L]

# User configurations routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^user/configurations/?$ user/configurations.php [QSA,L]

# User QR codes routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^user/qr-codes/?$ user/qr-codes.php [QSA,L]

# User success URL routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^user/success-url/?$ user/success-url.php [QSA,L]

# SMM Payment page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^smm-payment/?$ user/smm-payment.php [QSA,L]

# SMM Link routing - redirect to Request/SMM directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^smm_link/?$ Request/SMM/index.php [QSA,L]

# Payment success page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^payment/sample-success-page/?$ payment/sample-success-page.php [QSA,L]

# Auth routing - specific files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^auth/google_callback/?$ auth/google_callback.php [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^auth/complete_google_registration/?$ auth/complete_google_registration.php [QSA,L]

# Password reset routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^reset_password/?$ reset_password.php [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^forgot_password/?$ forgot_password.php [QSA,L]

# Email verification routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^verify_email/?$ verify_email.php [QSA,L]

# Login and register routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^login/?$ login.php [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^register/?$ register.php [QSA,L]



# ============================================
# Admin Clean URLs
# ============================================

# Admin configurations
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/config/?$ admin/configurations.php [QSA,L]

# Admin reports
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/reports/?$ admin/reports.php [QSA,L]

# Admin settings
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/settings/?$ admin/settings.php [QSA,L]

# ============================================
# API Routing
# ============================================

# API endpoints routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1.php [QSA,L]

# ============================================
# Error Handling
# ============================================

# Custom error pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# ============================================
# Performance & Security
# ============================================

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?cambo-pay\.com [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?localhost [NC]
RewriteRule \.(jpg|jpeg|png|gif|css|js)$ - [F]
