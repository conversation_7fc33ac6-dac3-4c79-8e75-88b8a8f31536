<?php
// Set CORS headers to allow iframe embedding from any domain
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type");

// Use proper CSP with frame-ancestors
header("Content-Security-Policy: frame-ancestors *");

// Add Cross-Origin-Resource-Policy header
header("Cross-Origin-Resource-Policy: cross-origin");

// Connect to the database
include 'db_config.php';

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Function to check if a column exists in a table
function columnExists($conn, $table, $column) {
    $query = "SHOW COLUMNS FROM $table LIKE '$column'";
    $result = $conn->query($query);
    return ($result && $result->num_rows > 0);
}

// Get the parameters from the URL
$qrData = isset($_GET['qr']) ? $_GET['qr'] : '';
$amount = isset($_GET['amount']) ? $_GET['amount'] : '';
$md5 = isset($_GET['md5']) ? $_GET['md5'] : '';
$username = isset($_GET['username']) ? $_GET['username'] : '';
$shortHash = isset($_GET['short_hash']) ? $_GET['short_hash'] : substr($md5, 0, 8);
$profileId = isset($_GET['profile_id']) ? $_GET['profile_id'] : '';
$companyName = isset($_GET['company_name']) ? $_GET['company_name'] : 'CHHEAN-SMM.NET';
$bakongId = isset($_GET['bakong_id']) ? $_GET['bakong_id'] : 'leapmeng_hai1@trmc';

// Get user's IP address
function getClientIP() {
    // For development/testing - simulate a real IP address
    // Comment this out before deploying to production
    if ($_SERVER['REMOTE_ADDR'] == '::1' || $_SERVER['REMOTE_ADDR'] == '127.0.0.1') {
        // Generate a random realistic IP for testing
        $testIPs = [
            '*************',  // TEST-NET-3 block for documentation
            '*************',  // TEST-NET-2 block for documentation
            '***********',    // TEST-NET-1 block for documentation
            '************',   // Private network
            '********',       // Private network
            '2001:db8:85a3:8d3:1319:8a2e:370:7348' // IPv6 documentation block
        ];
        return $testIPs[array_rand($testIPs)];
    }

    // Normal IP detection for production
    $ipAddress = '';
    if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
    else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_X_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
    else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_FORWARDED']))
        $ipAddress = $_SERVER['HTTP_FORWARDED'];
    else if(isset($_SERVER['REMOTE_ADDR']))
        $ipAddress = $_SERVER['REMOTE_ADDR'];
    else
        $ipAddress = 'UNKNOWN';
    return $ipAddress;
}

$userIpAddress = getClientIP();

// Log the received parameters for debugging
error_log("QRCODE.PHP - Received parameters: amount=$amount, username=$username, profile_id=$profileId, md5=$md5, ip=$userIpAddress");

// Get current time for the database
$currentTime = date('Y-m-d H:i:s');

// Calculate expiration time (6 hours from now)
$expirationTime = date('Y-m-d H:i:s', strtotime('+6 hours'));

// --- API expiry and amount check ---
$apiExpired = false;
$bakongId = '';
if ($profileId) {
    $sql = "SELECT api_expiry_date, bakong_id FROM users WHERE profile_id = '" . mysqli_real_escape_string($conn, $profileId) . "'";
    $result = mysqli_query($conn, $sql);
    if ($result && $row = mysqli_fetch_assoc($result)) {
        $apiExpired = strtotime($row['api_expiry_date']) < time();
        $bakongId = $row['bakong_id'];
    }
}
$maxAllowedAmount = $apiExpired ? 0.10 : 1000;

// Track failed attempts in session per bakong_id
session_start();
if (!isset($_SESSION['bakong_attempts'])) {
    $_SESSION['bakong_attempts'] = [];
}
if (!isset($_SESSION['bakong_attempts'][$bakongId])) {
    $_SESSION['bakong_attempts'][$bakongId] = 0;
}
if ($_SESSION['bakong_attempts'][$bakongId] >= 3) {
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Free Subscription Exceeded</title>
      <style>
        body { background: #fff; display: flex; align-items: center; justify-content: center; height: 100vh; margin: 0; }
        .error-card {
          width: 280px;
          background: #fff;
          border-radius: 20px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          padding: 40px 20px 32px 20px;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .error-icon { font-size: 56px; color: #e32219; margin-bottom: 18px; }
        .api-badge { display: inline-block; background: #ffeaea; color: #e32219; font-weight: 700; border-radius: 5px; padding: 3px 12px; font-size: 13px; margin-bottom: 16px; letter-spacing: 0.5px; }
        .error-title { font-size: 22px; font-weight: 800; color: #e32219; margin-bottom: 10px; letter-spacing: 1px; }
        .error-message { font-size: 15px; color: #333; margin-bottom: 0; line-height: 1.6; }
      </style>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    </head>
    <body>
      <div class="error-card">
        <div class="error-icon"><i class="fas fa-ban"></i></div>
        <div class="api-badge">Free Subscription Exceeded</div>
        <div class="error-title">Limit Reached</div>
        <div class="error-message">
          Your free subscription has been exceeded.<br>
          Please pay to use the full service.
        </div>
      </div>
    </body>
    </html>
    <?php
    exit;
}
if ($apiExpired && floatval($amount) > 0.10) {
    $_SESSION['bakong_attempts'][$bakongId]++;
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Amount Exceeded</title>
      <style>
        body { background: #fff; display: flex; align-items: center; justify-content: center; height: 100vh; margin: 0; }
        .error-card {
          width: 280px;
          background: #fff;
          border-radius: 20px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          padding: 40px 20px 32px 20px;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .error-icon { font-size: 56px; color: #e32219; margin-bottom: 18px; }
        .error-title { font-size: 22px; font-weight: 800; color: #e32219; margin-bottom: 10px; letter-spacing: 1px; }
        .error-message { font-size: 15px; color: #333; margin-bottom: 0; line-height: 1.6; }
      </style>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    </head>
    <body>
      <div class="error-card">
        <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
        <div class="error-title">Amount Exceeded</div>
        <div class="error-message">Amount exceeds the allowed limit of <b>$0.10</b> for your current API status.<br>Please enter a lower amount or upgrade your API plan.</div>
      </div>
    </body>
    </html>
    <?php
    exit;
}

// Check if the entry already exists
$checkStmt = $conn->prepare("SELECT id FROM qrcode_data WHERE md5 = ?");
$checkStmt->bind_param("s", $md5);
$checkStmt->execute();
$result = $checkStmt->get_result();

if ($result->num_rows > 0) {
    // Entry exists, update it
    // First check if it already has a transaction_id
    $getTransactionQuery = "SELECT transaction_id FROM qrcode_data WHERE md5 = ?";
    $getTransactionStmt = $conn->prepare($getTransactionQuery);
    $getTransactionStmt->bind_param("s", $md5);
    $getTransactionStmt->execute();
    $transactionResult = $getTransactionStmt->get_result();
    $qrRecord = $transactionResult->fetch_assoc();

    if (empty($qrRecord['transaction_id'])) {
        // No transaction ID, generate one and add it
        $transactionId = "TXN" . time() . rand(1000, 9999);

        // Check if ip_address column exists
        if (columnExists($conn, 'qrcode_data', 'ip_address')) {
            $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, username = ?, profile_id = ?, transaction_id = ?, status = 'pending', ip_address = ? WHERE md5 = ?");
            $updateStmt->bind_param("sdsssss", $qrData, $amount, $username, $profileId, $transactionId, $userIpAddress, $md5);
        } else {
            $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, username = ?, profile_id = ?, transaction_id = ?, status = 'pending' WHERE md5 = ?");
            $updateStmt->bind_param("sdssss", $qrData, $amount, $username, $profileId, $transactionId, $md5);
        }
    } else {
        // Has transaction ID, just update the fields
        if (columnExists($conn, 'qrcode_data', 'ip_address')) {
            $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, username = ?, profile_id = ?, ip_address = ? WHERE md5 = ?");
            $updateStmt->bind_param("sdssss", $qrData, $amount, $username, $profileId, $userIpAddress, $md5);
        } else {
            $updateStmt = $conn->prepare("UPDATE qrcode_data SET qr_data = ?, amount = ?, username = ?, profile_id = ? WHERE md5 = ?");
            $updateStmt->bind_param("sdsss", $qrData, $amount, $username, $profileId, $md5);
        }
    }

    if (!$updateStmt->execute()) {
        error_log("Error updating qrcode_data record: " . $updateStmt->error);
    }
    $updateStmt->close();
    $getTransactionStmt->close();
} else {
    // Entry doesn't exist, insert it
    $transactionId = "TXN" . time() . rand(1000, 9999);

    // Check if ip_address column exists
    if (columnExists($conn, 'qrcode_data', 'ip_address')) {
        $insertStmt = $conn->prepare("INSERT INTO qrcode_data (qr_data, amount, md5, short_hash, username, profile_id, created_at, transaction_id, status, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?)");
        $insertStmt->bind_param("sdsssssss", $qrData, $amount, $md5, $shortHash, $username, $profileId, $currentTime, $transactionId, $userIpAddress);
    } else {
        $insertStmt = $conn->prepare("INSERT INTO qrcode_data (qr_data, amount, md5, short_hash, username, profile_id, created_at, transaction_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
        $insertStmt->bind_param("sdssssss", $qrData, $amount, $md5, $shortHash, $username, $profileId, $currentTime, $transactionId);
    }

    if (!$insertStmt->execute()) {
        error_log("Error inserting qrcode_data record: " . $insertStmt->error);
    }
    $insertStmt->close();
}

$checkStmt->close();
// Note: Don't close the database connection yet as we might need it later in the file

// If profile_id exists, create or update a payment record
if (!empty($profileId)) {
    // Get user_id from profile_id
    $userQuery = "SELECT id FROM users WHERE profile_id = ?";
    $userStmt = $conn->prepare($userQuery);
    $userStmt->bind_param("s", $profileId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();

    if ($userResult->num_rows > 0) {
        $userData = $userResult->fetch_assoc();
        $userId = $userData['id'];

        // Create a unique transaction ID
        $transactionId = "TXN" . time() . rand(1000, 9999);

        // Check if payment record exists for this md5
        $checkPaymentQuery = "SELECT id FROM payments WHERE user_id = ? AND transaction_id LIKE 'TXN%'";
        if (columnExists($conn, 'payments', 'md5')) {
            $checkPaymentQuery = "SELECT id FROM payments WHERE transaction_id = ? OR (user_id = ? AND md5 = ?)";
        }
        $checkPaymentStmt = $conn->prepare($checkPaymentQuery);

        if ($checkPaymentStmt === false) {
            error_log("Error preparing payment check query: " . $conn->error);
        } else {
            if (columnExists($conn, 'payments', 'md5')) {
                $checkPaymentStmt->bind_param("sis", $transactionId, $userId, $md5);
            } else {
                $checkPaymentStmt->bind_param("i", $userId);
            }
            $checkPaymentStmt->execute();
            $checkPaymentResult = $checkPaymentStmt->get_result();

            if ($checkPaymentResult->num_rows == 0) {
                // Create pending payment record with expiration time
                $status = "pending";
                $remark = "KHQR Payment";
                $paymentMethod = "KHQR";

                // Check which columns exist in the payments table
                $insertFields = "user_id, amount, status, transaction_id";
                $insertPlaceholders = "?, ?, ?, ?";
                $insertTypes = "idss";
                $insertValues = [$userId, $amount, $status, $transactionId];

                if (columnExists($conn, 'payments', 'md5')) {
                    $insertFields .= ", md5";
                    $insertPlaceholders .= ", ?";
                    $insertTypes .= "s";
                    $insertValues[] = $md5;
                }

                if (columnExists($conn, 'payments', 'remark')) {
                    $insertFields .= ", remark";
                    $insertPlaceholders .= ", ?";
                    $insertTypes .= "s";
                    $insertValues[] = $remark;
                }

                if (columnExists($conn, 'payments', 'payment_method')) {
                    $insertFields .= ", payment_method";
                    $insertPlaceholders .= ", ?";
                    $insertTypes .= "s";
                    $insertValues[] = $paymentMethod;
                }

                if (columnExists($conn, 'payments', 'date')) {
                    $insertFields .= ", date";
                    $insertPlaceholders .= ", ?";
                    $insertTypes .= "s";
                    $insertValues[] = $currentTime;
                }

                if (columnExists($conn, 'payments', 'expires_at')) {
                    $insertFields .= ", expires_at";
                    $insertPlaceholders .= ", ?";
                    $insertTypes .= "s";
                    $insertValues[] = $expirationTime;
                }

                $insertQuery = "INSERT INTO payments ($insertFields) VALUES ($insertPlaceholders)";
                $insertPaymentStmt = $conn->prepare($insertQuery);

                if ($insertPaymentStmt === false) {
                    error_log("Error preparing payment insert query: " . $conn->error);
                } else {
                    // Dynamically bind parameters
                    $bindParams = [$insertTypes];
                    foreach ($insertValues as $key => $value) {
                        $bindParams[] = &$insertValues[$key];
                    }
                    call_user_func_array([$insertPaymentStmt, 'bind_param'], $bindParams);

                    $insertPaymentStmt->execute();

                    if ($insertPaymentStmt->affected_rows > 0) {
                        error_log("Created pending payment record for user_id=$userId, profile_id=$profileId, transaction_id=$transactionId, expires_at=$expirationTime");
                    } else {
                        error_log("Error creating payment record: " . $insertPaymentStmt->error);
                    }

                    $insertPaymentStmt->close();
                }
            } else {
                // Update existing payment record
                $paymentData = $checkPaymentResult->fetch_assoc();
                $paymentId = $paymentData['id'];

                $updateFields = [];

                if (columnExists($conn, 'payments', 'expires_at')) {
                    $updateFields[] = "expires_at = ?";
                }

                if (!empty($updateFields)) {
                    $updateQuery = "UPDATE payments SET " . implode(", ", $updateFields) . " WHERE id = ?";
                    $updatePaymentStmt = $conn->prepare($updateQuery);

                    if ($updatePaymentStmt === false) {
                        error_log("Error preparing payment update query: " . $conn->error);
                    } else {
                        $updateTypes = "si"; // s for expires_at, i for id
                        $updatePaymentStmt->bind_param($updateTypes, $expirationTime, $paymentId);
                        $updatePaymentStmt->execute();

                        if ($updatePaymentStmt->affected_rows > 0) {
                            error_log("Updated payment record id=$paymentId with new expiration time=$expirationTime");
                        }

                        $updatePaymentStmt->close();
                    }
                }
            }

            $checkPaymentStmt->close();
        }

        $userStmt->close();
    }
}

// Create a scheduled task to expire old transactions
// This will run once per page load but is lightweight
cleanupExpiredTransactions($conn);

function cleanupExpiredTransactions($conn) {
    $currentTime = date('Y-m-d H:i:s');

    // Get expiration time (6 hours ago)
    $expirationTime = date('Y-m-d H:i:s', strtotime('-6 hours'));

    // First check if the status column exists
    if (columnExists($conn, 'qrcode_data', 'status')) {
        // Update status of expired pending transactions in qrcode_data table
        $updateQuery = "UPDATE qrcode_data SET status = 'failed' WHERE status = 'pending' AND created_at < ?";
        $updateStmt = $conn->prepare($updateQuery);

        if ($updateStmt) {
            $updateStmt->bind_param("s", $expirationTime);
            $updateStmt->execute();

            if ($updateStmt->affected_rows > 0) {
                error_log("Expired " . $updateStmt->affected_rows . " pending transactions in qrcode_data");
            }

            $updateStmt->close();
        } else {
            // Log the error but don't stop execution
            error_log("Error preparing cleanup statement for qrcode_data: " . $conn->error);
        }
    } else {
        error_log("Cannot expire transactions: status column does not exist in qrcode_data table");
        error_log("Please run update_qrcode_data_table.php to add required columns");
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <title>qrcode - KHQR</title>
  <meta property="og:title" content="qrcode - KHQR" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta charset="utf-8" />
  <meta property="twitter:card" content="summary_large_image" />

  <style data-tag="reset-style-sheet">
    html {
      line-height: 1.15;
    }

    body {
      margin: 0;
    }

    * {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
    }

    p,
    li,
    ul,
    pre,
    div,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    figure,
    blockquote,
    figcaption {
      margin: 0;
      padding: 0;
    }

    button {
      background-color: transparent;
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font-family: inherit;
      font-size: 100%;
      line-height: 1.15;
      margin: 0;
    }

    button,
    select {
      text-transform: none;
    }

    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
      -webkit-appearance: button;
    }

    button::-moz-focus-inner,
    [type="button"]::-moz-focus-inner,
    [type="reset"]::-moz-focus-inner,
    [type="submit"]::-moz-focus-inner {
      border-style: none;
      padding: 0;
    }

    button:-moz-focus,
    [type="button"]:-moz-focus,
    [type="reset"]:-moz-focus,
    [type="submit"]:-moz-focus {
      outline: 1px dotted ButtonText;
    }

    a {
      color: inherit;
      text-decoration: inherit;
    }

    input {
      padding: 2px 4px;
    }

    img {
      display: block;
    }

    html {
      scroll-behavior: smooth
    }
  </style>
  <style data-tag="default-style-sheet">
    html {
      font-family: Inter;
      font-size: 16px;
    }

    body {
      font-weight: 400;
      font-style: normal;
      text-decoration: none;
      text-transform: none;
      letter-spacing: normal;
      line-height: 1.15;
      color: var(--dl-color-gray-black);
      background-color: var(--dl-color-gray-white);


    }

    .qrcode-loadingpic {
      /* Set initial rotation */
      transform: rotate(0deg);

      /* Define the animation */
      animation: rotateAnimation 1s linear infinite;
    }

    /* Define the keyframes for the rotation animation */
    @keyframes rotateAnimation {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <link rel="shortcut icon" href="img/unnamed.png" type="icon/png" sizes="32x32" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Dangrek:wght@400&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css" />
</head>

<body>
  <link rel="stylesheet" href="css/style.css" />
  <div>
    <link href="css/qrcode.css" rel="stylesheet" />

    <div class="qrcode-container">
      <div class="qrcode-body body">
        <img src="https://link.payway.com.kh/images/loading.svg" alt="image" class="qrcode-loadingpic" />
        <span class="qrcode-minutes">
          <span id="countdown">3:00</span>
          <br />
          <br />
        </span>
        <span id="name" class="qrcode-name"><b style="font-size:13px;"><span id="company-name-display"><?php echo htmlspecialchars($companyName); ?></span> - By CamboPay</b></span>
        <span id="currency" class="qrcode-currency">$</span>
        <span id="amount" class="qrcode-amount">
          0.00
        </span>
        <div class="qrcode-head">
          <div class="qrcode-header">
            <div class="qrcode-container1">
              <div class="qrcode-container2"></div>
              <div class="qrcode-container3">
                <div class="qrcode-container4 qrhrader"></div>
                <img alt="image" src="img/khqr%20logo-200h.png" class="qrcode-image logo" />
              </div>
            </div>
          </div>
        </div>
        <div class="qrcode-line line">
          <div class="qrcode-qrcode qrcode">
            <img id="qr-image" alt="image" src="" class="qrcode-qr" />
            <img id="logo" alt="image" src="https://checkout.payway.com.kh/images/usd-khqr-logo.svg"
              class="qrcode-logo" />
          </div>
        </div>
        <img alt="image"
          src="img/payment_icons-cd5e952dde3b886dea1fd1b983d43ce372f1692dec253808ec654096d2feb701-200h.png"
          class="qrcode-banklogo" />
      </div>
    </div>
  </div>
</body>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Store profile_id in sessionStorage for later use
    const profileIdFromUrl = getParameterByName("profile_id");
    if (profileIdFromUrl) {
      sessionStorage.setItem('profileId', profileIdFromUrl);
    }

    // Function to check MD5 API
    async function checkMd5Api() {
      const url = 'https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5';
      const md5FromUrl = getParameterByName("md5");
      const bakongId = "<?php echo addslashes($bakongId); ?>" || sessionStorage.getItem('bakongId') || "bundavit@wing";
      const profileId = getParameterByName("profile_id") || sessionStorage.getItem('profileId') || '';

      console.log("Checking transaction with MD5:", md5FromUrl);
      console.log("Using bakongId:", bakongId);
      console.log("Using profileId:", profileId);

      const data = {
        md5: md5FromUrl
      };

      try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.8SpnhewDbWX6B8PxheTpuixFYowz2PgdvrcBlkFHAFQ',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const responseData = await response.json();
          console.log("API response:", responseData);

        if (responseData.responseCode === 0) {
            // Check if toAccountId matches the bakongId from profile
            console.log("Transaction found. Comparing toAccountId:", responseData.data.toAccountId, "with bakongId:", bakongId);

            // Extract payer information from the API response
            const payerAccountId = responseData.data.fromAccountId || '';
            console.log("Payer account ID:", payerAccountId);

            // Extract the hash from the API response
            const fullHash = responseData.data.hash || '';
            console.log("Full hash from API:", fullHash);

            // Extract the first 8 characters of the hash for the short hash
            const shortHash = fullHash.substring(0, 8);
            console.log("Short hash (first 8 chars):", shortHash);

            if (responseData.data.toAccountId === bakongId) {
              console.log("Account IDs match! Processing payment...");

              // Get the username from the URL parameter
              const usernameFromUrl = getParameterByName("username");
              const amountFromUrl = getParameterByName("amount");

              // Update payment status to success
              if (profileId) {
                console.log("Updating payment status for profile:", profileId);
                await updatePaymentStatus(profileId, md5FromUrl, amountFromUrl, usernameFromUrl, payerAccountId, shortHash);
              } else {
                console.log("No profile ID available, skipping payment status update");
              }

              // Redirect to tg.php with the necessary parameters
              window.location.href = `tg.php?amount=${amountFromUrl}&username=${usernameFromUrl}&profile_id=${profileId}&payer=${encodeURIComponent(payerAccountId)}&short_hash=${encodeURIComponent(shortHash)}`;
            } else {
              console.log("Account IDs don't match. Redirecting to successful.php");
              // Redirect to error.php if toAccountId doesn't match
              window.location.href = 'successful.php';
            }
          } else {
            // Handle other response codes or display a message
            console.log('API response code is not 0:', responseData.responseCode);
          }
        } else {
          console.error('Failed to fetch data:', response.status);
        }
      } catch (error) {
        console.error('Error during fetch:', error);
      }
    }

    // Call checkMd5Api every 5 seconds
    setInterval(checkMd5Api, 5000); // 5000 milliseconds = 5 seconds
  });

  // Function to get the value of a URL parameter
  function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
      results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return "";
    return decodeURIComponent(results[2].replace(/\+/g, " "));
  }

  // Get the "amount" and "qr" parameters from the URL
    const amountFromUrl = getParameterByName("amount");
    const qrFromUrl = getParameterByName("qr");

  // Company name is already set from PHP in the HTML
  // No need to set it again in JavaScript

  // Set the obtained amount to an HTML element with ID "amount"
    const amountElement = document.getElementById("amount");
    if (amountElement) {
    amountElement.textContent = amountFromUrl || "Default Amount"; // Set a default value if amountFromUrl is null
  }

  // Set the obtained qr to an HTML element with ID "qr-code"
  const qrCodeElement = document.getElementById("qr-image");
  if (qrCodeElement) {
    qrCodeElement.textContent = qrFromUrl || "Default QR"; // Set a default value if qrFromUrl is null

    // Generate the QR code image and set it as the source for an <img> tag
    const qrImage = document.getElementById("qr-image");
    if (qrImage) {
      qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=190x190&data=${qrFromUrl}`;
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Set the countdown duration in seconds
    let countdownDuration = 180; // 3 minutes * 60 seconds

    // Get the countdown element
    const countdownElement = document.getElementById("countdown");

    // Get profile_id from URL or sessionStorage
    const profileId = getParameterByName("profile_id") || sessionStorage.getItem('profileId') || '';

    // Update the countdown every second
    const countdownInterval = setInterval(function () {
      const minutes = Math.floor(countdownDuration / 60);
      const seconds = countdownDuration % 60;

      // Display the countdown in the format MM:SS
      countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

      // Decrease the countdown duration
      countdownDuration--;

      // Check if the countdown has reached 0
      if (countdownDuration < 0) {
        // Get md5 from URL
        const md5FromUrl = getParameterByName("md5");

        // Redirect to expire.php when the countdown reaches 0, passing profile_id and md5 if available
        let redirectUrl = 'expire.php';
        const params = [];

        if (profileId) {
          params.push(`profile_id=${encodeURIComponent(profileId)}`);
        }

        if (md5FromUrl) {
          params.push(`md5=${encodeURIComponent(md5FromUrl)}`);
        }

        if (params.length > 0) {
          redirectUrl += '?' + params.join('&');
        }

        window.location.href = redirectUrl;

        // Clear the interval to stop the countdown
        clearInterval(countdownInterval);
      }
    }, 1000); // Update every 1000 milliseconds (1 second)
  });

  // Function to update payment status
  async function updatePaymentStatus(profileId, md5, amount, username, payer, shortHash) {
    try {
      console.log(`Sending payment update: profile_id=${profileId}, md5=${md5}, amount=${amount}, username=${username}, payer=${payer}, short_hash=${shortHash}`);

      const response = await fetch('update_payment_status.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `profile_id=${encodeURIComponent(profileId)}&md5=${encodeURIComponent(md5)}&amount=${encodeURIComponent(amount)}&username=${encodeURIComponent(username)}&payer=${encodeURIComponent(payer)}&short_hash=${encodeURIComponent(shortHash)}`,
      });

      const result = await response.text();
      console.log('Payment status update result:', result);
      return result;
    } catch (error) {
      console.error('Error updating payment status:', error);
      return `Error: ${error.message}`;
    }
  }
</script>

<?php
// Close the database connection at the end of the file
if (isset($conn)) {
  mysqli_close($conn);
}
?>
</html>