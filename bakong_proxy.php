<?php
// bakong_proxy.php - Server-side proxy for Bakong API calls to avoid CORS issues
// Root level proxy that can be accessed from anywhere

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set CORS headers to allow requests from your frontend
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Log access attempt
error_log("Root Bakong Proxy Access - Method: " . $_SERVER['REQUEST_METHOD'] . " - Time: " . date('Y-m-d H:i:s'));

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Handle GET requests for testing
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'status' => 'Bakong Proxy is working',
        'message' => 'Send POST request with md5 parameter to use this proxy',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => $_SERVER['HTTP_HOST'] ?? 'unknown'
    ]);
    exit;
}

// Only allow POST requests for the actual API call
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get the request body
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate required data
if (!isset($data['md5']) || empty($data['md5'])) {
    http_response_code(400);
    echo json_encode(['error' => 'MD5 parameter is required']);
    exit;
}

// Bakong API configuration - Updated token
$bakong_api_url = 'https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5';
$bakong_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k';

// Prepare the data to send to Bakong API
$bakong_data = [
    'md5' => $data['md5']
];

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $bakong_api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($bakong_data),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $bakong_token
    ],
    CURLOPT_TIMEOUT => 30,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_MAXREDIRS => 3
]);

// Execute the request
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);

// Close cURL
curl_close($ch);

// Handle cURL errors
if ($response === false || !empty($curl_error)) {
    error_log("Bakong API cURL Error: " . $curl_error);
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to connect to Bakong API',
        'details' => $curl_error
    ]);
    exit;
}

// Handle HTTP errors
if ($http_code !== 200) {
    error_log("Bakong API HTTP Error: " . $http_code . " - Response: " . $response);
    http_response_code($http_code);
    echo json_encode([
        'error' => 'Bakong API returned error',
        'http_code' => $http_code,
        'response' => $response
    ]);
    exit;
}

// Decode and validate the response
$decoded_response = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("Bakong API JSON Decode Error: " . json_last_error_msg());
    http_response_code(500);
    echo json_encode([
        'error' => 'Invalid JSON response from Bakong API',
        'raw_response' => $response
    ]);
    exit;
}

// Log successful API call for debugging
error_log("Bakong API Success - MD5: " . $data['md5'] . " - Response Code: " . ($decoded_response['responseCode'] ?? 'unknown'));

// Return the response from Bakong API
echo json_encode($decoded_response);
?>
