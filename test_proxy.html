<!DOCTYPE html>
<html>
<head>
    <title>Test Bakong Proxy</title>
</head>
<body>
    <h1>Test Bakong Proxy</h1>
    <button onclick="testProxy()">Test POST Request</button>
    <div id="result"></div>

    <script>
        async function testProxy() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            const testData = {
                md5: 'ec86c0cca3f7c7de8d065a7ea350d6bc'
            };

            console.log('Sending POST request with data:', testData);

            try {
                const response = await fetch('/productmanage/bakong_proxy.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                // Get the raw response text first
                const responseText = await response.text();
                console.log('Raw response:', responseText);

                // Try to parse as JSON
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                    console.log('Parsed JSON:', responseData);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    resultDiv.innerHTML = '<h3>Raw Response (not JSON):</h3><pre>' + responseText + '</pre>';
                    return;
                }

                resultDiv.innerHTML = '<pre>' + JSON.stringify(responseData, null, 2) + '</pre>';
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
